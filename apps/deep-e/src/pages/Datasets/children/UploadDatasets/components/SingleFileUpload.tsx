import React from 'react';
import { Form, Input, Button, Upload, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { uploadDataset } from '@/pages/Datasets/api';
import { t } from '@/languages';

interface DatasetFormValues {
  datasetName: string;
  datasetDesc: string;
  file?: any;
}

interface SingleFileUploadProps {
  form: any;
}

export default function SingleFileUpload({ form }: SingleFileUploadProps) {
  const navigate = useNavigate();

  const handleSubmit = async (values: DatasetFormValues) => {
    console.log('handleSubmit called with values:', values);

    if (!values.file || !values.file.file) {
      message.error(t('请选择要上传的文件'));
      return;
    }

    const formData = new FormData();
    formData.append('datasetName', values.datasetName);
    formData.append('datasetDesc', values.datasetDesc);
    formData.append('file', values.file.file);
    formData.append('datasetType', 'singleFile');

    try {
      const res = await uploadDataset(formData);
      if (res.code === 0) {
        message.success(t('数据集上传成功'));
        navigate('/datasets');
      } else {
        message.error(t(`数据集上传失败: ${res.msg || ''}`));
      }
    } catch (error) {
      console.error(t('上传数据集时发生错误:'), error);
      message.error(t('上传数据集时发生错误'));
    }
  };

  return (
    <div className='max-w-[600px]'>
      <Form form={form} onFinish={handleSubmit} layout='vertical'>
        <Form.Item
          label={t('数据集名称')}
          name='datasetName'
          rules={[{ required: true, message: t('请输入数据集名称') }]}>
          <Input />
        </Form.Item>
        <Form.Item
          label={t('数据集描述')}
          name='datasetDesc'
          rules={[{ required: true, message: t('请输入数据集描述') }]}>
          <Input.TextArea rows={4} />
        </Form.Item>
        <Form.Item
          label={t('上传文件')}
          name='file'
          rules={[{ required: true, message: t('请选择要上传的文件') }]}>
          <Upload.Dragger
            beforeUpload={() => false}
            multiple={false}
            showUploadList={true}>
            <p className='ant-upload-drag-icon'>
              <UploadOutlined />
            </p>
            <p className='ant-upload-text'>{t('点击或拖拽文件到此区域上传')}</p>
            <p className='ant-upload-hint'>
              {t(
                '支持单文件上传，文件大小不超过 100MB，支持 .csv, .json, .txt 等格式',
              )}
            </p>
          </Upload.Dragger>
        </Form.Item>
        <Form.Item>
          <div className='flex justify-center'>
            <Button type='primary' htmlType='submit' size='large'>
              {t('提交')}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
}
