import React, { useEffect, useState, useMemo } from 'react';
import { Table, Image, Modal, Card, Tag, Button, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';
import { t } from '@/languages';
import AnnotatedImage, {
  COCOAnnotation,
  COCOCategory,
  COCOImage,
} from './AnnotatedImage';
import AnnotationLegend from './AnnotationLegend';

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({ datasetUuid, changTotalData }) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Modal相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImageData, setSelectedImageData] = useState<{
    imageUrl: string;
    annotations: COCOAnnotation[];
    categories: COCOCategory[];
    imageInfo: COCOImage;
  } | null>(null);

  // 虚拟数据状态
  const [useVirtualData, setUseVirtualData] = useState(false);

  // 生成虚拟数据集
  const generateVirtualDataset = (): DatasetDetailItem[] => {
    const sampleImages = [
      'https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1546527868-ccb7ee7dfa6a?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop',
    ];

    const sampleQuestions = [
      '这张图片中有多少个人？',
      '图片中的动物是什么品种？',
      '这辆车是什么颜色的？',
      '图片中的建筑物有什么特征？',
      '这个场景发生在什么时间？',
    ];

    const sampleAnswers = [
      '图片中有2个人，一个成年人和一个小孩。',
      '这是一只金毛犬，看起来很友好。',
      '这辆车是红色的，是一辆SUV。',
      '这是一座现代化的办公楼，有玻璃幕墙。',
      '这个场景发生在白天，阳光明媚。',
    ];

    return Array.from({ length: 15 }, (_, index) => ({
      id: index + 1,
      createdAt: Date.now() - index * 86400000,
      createdBy: 1,
      updatedAt: Date.now(),
      updatedBy: 1,
      fileUuid: `file-uuid-${index + 1}`,
      datasetDetail: {
        image_url: sampleImages[index % sampleImages.length],
        question: sampleQuestions[index % sampleQuestions.length],
        answer: sampleAnswers[index % sampleAnswers.length],
        category: ['人物', '动物', '车辆', '建筑', '场景'][index % 5],
        confidence: (0.8 + Math.random() * 0.2).toFixed(3),
        timestamp: new Date(Date.now() - index * 86400000).toISOString(),
        messages: [
          {
            role: 'user',
            content: sampleQuestions[index % sampleQuestions.length],
          },
          {
            role: 'assistant',
            content: sampleAnswers[index % sampleAnswers.length],
          },
        ],
      },
    }));
  };

  // 切换虚拟数据
  const toggleVirtualData = () => {
    setUseVirtualData(!useVirtualData);
    if (!useVirtualData) {
      // 切换到虚拟数据
      const virtualData = generateVirtualDataset();
      setDataList(virtualData);
      setPagination({
        current: 1,
        pageSize: 10,
        total: virtualData.length,
      });
      changTotalData(virtualData.length);
    } else {
      // 切换回真实数据
      fetchDetail();
    }
  };

  // 生成模拟COCO格式标注数据
  const generateMockCOCOData = (imageUrl: string) => {
    const mockCategories: COCOCategory[] = [
      { id: 1, name: 'person', supercategory: 'person' },
      { id: 2, name: 'car', supercategory: 'vehicle' },
      { id: 3, name: 'dog', supercategory: 'animal' },
      { id: 4, name: 'cat', supercategory: 'animal' },
      { id: 5, name: 'bicycle', supercategory: 'vehicle' },
    ];

    const mockImageInfo: COCOImage = {
      id: 1,
      file_name: imageUrl.split('/').pop() || 'image.jpg',
      width: 640,
      height: 480,
      url: imageUrl,
    };

    const mockAnnotations: COCOAnnotation[] = [
      {
        id: 1,
        image_id: 1,
        category_id: 1,
        bbox: [100, 50, 120, 200], // [x, y, width, height]
        area: 24000,
        iscrowd: 0,
        segmentation: [[100, 50, 220, 50, 220, 250, 100, 250]],
      },
      {
        id: 2,
        image_id: 1,
        category_id: 2,
        bbox: [300, 150, 200, 100],
        area: 20000,
        iscrowd: 0,
        segmentation: [[300, 150, 500, 150, 500, 250, 300, 250]],
      },
      {
        id: 3,
        image_id: 1,
        category_id: 3,
        bbox: [50, 300, 80, 60],
        area: 4800,
        iscrowd: 0,
      },
    ];

    return {
      imageUrl,
      annotations: mockAnnotations,
      categories: mockCategories,
      imageInfo: mockImageInfo,
    };
  };

  // 处理图片点击事件
  const handleImageClick = (imageUrl: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发行点击
    const cocoData = generateMockCOCOData(imageUrl);
    setSelectedImageData(cocoData);
    setModalVisible(true);
  };
  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({ datasetUuid, page, pageSize });
      if (res.code === 0) {
        const list = Array.isArray(res.data) ? res.data : res.data.list;
        setDataList(list || []); // 只保存列表数据，不再设置详情

        if (res.data.page) {
          setPagination({
            current: res.data.page,
            pageSize: res.data.pageSize || 10,
            total: res.data.total || 0,
          });
          changTotalData(res.data.total || 0);
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (record: DatasetDetailItem) => {
    setExpandedRowKeys((prev) =>
      prev.includes(String(record.id))
        ? prev.filter((key) => key !== String(record.id))
        : [...prev, String(record.id)],
    );
  };
  const columns: ColumnsType<DatasetDetailItem> = useMemo(() => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());
          // 判断是否为图片列
          const isImageCol =
            key.toLowerCase().includes('image') ||
            key.toLowerCase().includes('img') ||
            key.toLowerCase().includes('pic') ||
            key.toLowerCase().includes('url');
          // 支持无扩展名的图片链接（如 unsplash）
          const isImgUrl =
            typeof value === 'string' && /^https?:\/\//.test(value);
          if (isImageCol && isImgUrl) {
            return (
              <div style={{ position: 'relative' }}>
                <Image
                  src={value}
                  alt={t('图片')}
                  style={{
                    maxWidth: 120,
                    maxHeight: 80,
                    objectFit: 'contain',
                    borderRadius: 4,
                    cursor: 'pointer',
                  }}
                  preview={false} // 禁用默认预览，使用自定义Modal
                  placeholder={
                    <div
                      style={{ width: 120, height: 80, background: '#f0f0f0' }}
                    />
                  }
                  onClick={(event) => handleImageClick(value, event)}
                />
                <div
                  style={{
                    position: 'absolute',
                    top: 2,
                    right: 2,
                    background: 'rgba(0,0,0,0.6)',
                    color: 'white',
                    padding: '2px 6px',
                    borderRadius: 4,
                    fontSize: '10px',
                  }}>
                  {t('标注')}
                </div>
              </div>
            );
          }
          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`$${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`$${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  }, [dataList, expandedRowKeys]);

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <>
      {/* 控制按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type={useVirtualData ? 'primary' : 'default'}
            onClick={toggleVirtualData}>
            {useVirtualData ? t('切换到真实数据') : t('切换到虚拟数据')}
          </Button>
          {useVirtualData && (
            <span style={{ color: '#1890ff', fontSize: '12px' }}>
              {t('当前显示虚拟数据，点击图片可查看COCO标注信息')}
            </span>
          )}
        </Space>
      </div>

      <Table
        bordered
        rowKey='id'
        columns={columns}
        dataSource={dataList}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          style: { cursor: 'pointer' },
        })}
        scroll={{ x: 'max-content' }}
      />

      {/* COCO标注信息Modal */}
      <Modal
        title={t('图片标注信息 (COCO格式)')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}>
        {selectedImageData && (
          <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            {/* 图片标注可视化 */}
            <Card
              title={t('图片标注可视化')}
              size='small'
              style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', gap: 16 }}>
                <div style={{ flex: 2 }}>
                  <AnnotatedImage
                    imageUrl={selectedImageData.imageUrl}
                    annotations={selectedImageData.annotations}
                    categories={selectedImageData.categories}
                    imageInfo={selectedImageData.imageInfo}
                    maxWidth={600}
                    showControls={true}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ marginBottom: 16 }}>
                    <p>
                      <strong>{t('文件名')}:</strong>{' '}
                      {selectedImageData.imageInfo.file_name}
                    </p>
                    <p>
                      <strong>{t('尺寸')}:</strong>{' '}
                      {selectedImageData.imageInfo.width} ×{' '}
                      {selectedImageData.imageInfo.height}
                    </p>
                    <p>
                      <strong>ID:</strong> {selectedImageData.imageInfo.id}
                    </p>
                  </div>

                  {/* <AnnotationLegend
                    categories={selectedImageData.categories}
                    annotations={selectedImageData.annotations}
                  /> */}
                </div>
              </div>
            </Card>

            {/* 类别信息 */}
            <Card
              title={t('标注类别')}
              size='small'
              style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                {selectedImageData.categories.map((category) => (
                  <Tag key={category.id} color='blue'>
                    {category.name} (ID: {category.id})
                  </Tag>
                ))}
              </div>
            </Card>

            {/* 标注信息 */}
            <Card title={t('标注详情')} size='small'>
              {selectedImageData.annotations.map((annotation, index) => {
                const category = selectedImageData.categories.find(
                  (cat) => cat.id === annotation.category_id,
                );
                return (
                  <div
                    key={annotation.id}
                    style={{
                      marginBottom: 16,
                      padding: 12,
                      border: '1px solid #f0f0f0',
                      borderRadius: 6,
                    }}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: 8,
                      }}>
                      <Tag color='green'>
                        {t('标注')} {index + 1}
                      </Tag>
                      <Tag color='orange'>
                        {category?.name || t('未知类别')}
                      </Tag>
                    </div>
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: 8,
                        fontSize: '12px',
                      }}>
                      <div>
                        <strong>{t('标注ID')}:</strong> {annotation.id}
                      </div>
                      <div>
                        <strong>{t('类别ID')}:</strong> {annotation.category_id}
                      </div>
                      <div>
                        <strong>{t('边界框')}:</strong> [
                        {annotation.bbox.join(', ')}]
                      </div>
                      <div>
                        <strong>{t('面积')}:</strong> {annotation.area}
                      </div>
                      <div>
                        <strong>{t('是否群体')}:</strong>{' '}
                        {annotation.iscrowd ? t('是') : t('否')}
                      </div>
                      {annotation.segmentation && (
                        <div style={{ gridColumn: '1 / -1' }}>
                          <strong>{t('分割信息')}:</strong>
                          <div
                            style={{
                              maxHeight: 60,
                              overflowY: 'auto',
                              marginTop: 4,
                              padding: 4,
                              background: '#f8f8f8',
                              borderRadius: 4,
                            }}>
                            {JSON.stringify(annotation.segmentation)}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </Card>
          </div>
        )}
      </Modal>
    </>
  );
};

export default DataPart;
