import React, { useEffect, useState, useMemo } from 'react';
import { Table, Tag, Button, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';
import { t } from '@/languages';

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({ datasetUuid, changTotalData }) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 虚拟数据状态
  const [useVirtualData, setUseVirtualData] = useState(false);

  // 新的数据结构类型定义
  interface QAItem {
    question: string;
    answer: string;
    lang: 'zh' | 'en';
    type: string;
  }

  interface AnnotationGroup {
    label: string;
    annotations: Array<{
      id: string;
      label: string;
      bboxes: number[][];
      attributes?: Record<string, any>;
      segmentation?: number[][];
    }>;
  }

  interface ImageItem {
    image_id: string;
    annotationGroups: AnnotationGroup[];
  }

  interface NewDataStructure {
    qa: QAItem[];
    images: ImageItem[];
  }

  // 生成新格式的虚拟数据集
  const generateVirtualDataset = (): DatasetDetailItem[] => {
    // 新的JSON数据结构
    const newDataStructure: NewDataStructure = {
      qa: [
        {
          question: '这张图片中有多少个人？',
          answer: '图片中有2个人，一个成年人和一个小孩。',
          lang: 'zh',
          type: 'object_recognition',
        },
        {
          question: 'What animals can you see in this image?',
          answer: 'I can see a golden retriever dog that looks very friendly.',
          lang: 'en',
          type: 'object_recognition',
        },
        {
          question: '这辆车是什么颜色的？',
          answer: '这辆车是红色的，是一辆SUV。',
          lang: 'zh',
          type: 'color_analysis',
        },
        {
          question:
            'What are the characteristics of the building in the image?',
          answer: 'This is a modern office building with glass curtain walls.',
          lang: 'en',
          type: 'scene_analysis',
        },
        {
          question: '这个场景发生在什么时间？',
          answer: '这个场景发生在白天，阳光明媚。',
          lang: 'zh',
          type: 'action_analysis',
        },
      ],
      images: [
        {
          image_id: 'img_001',
          annotationGroups: [
            {
              label: 'person',
              annotations: [
                {
                  id: 'ann_001',
                  label: 'person',
                  bboxes: [[100, 50, 120, 200]],
                  attributes: { age: 'adult', gender: 'male' },
                },
                {
                  id: 'ann_002',
                  label: 'person',
                  bboxes: [[250, 80, 80, 150]],
                  attributes: { age: 'child', gender: 'female' },
                },
              ],
            },
          ],
        },
        {
          image_id: 'img_002',
          annotationGroups: [
            {
              label: 'animal',
              annotations: [
                {
                  id: 'ann_003',
                  label: 'dog',
                  bboxes: [[50, 100, 200, 150]],
                  attributes: { breed: 'golden_retriever', color: 'golden' },
                },
              ],
            },
          ],
        },
        {
          image_id: 'img_003',
          annotationGroups: [
            {
              label: 'vehicle',
              annotations: [
                {
                  id: 'ann_004',
                  label: 'car',
                  bboxes: [[300, 150, 200, 100]],
                  attributes: { color: 'red', type: 'SUV' },
                },
              ],
            },
          ],
        },
        {
          image_id: 'img_004',
          annotationGroups: [
            {
              label: 'building',
              annotations: [
                {
                  id: 'ann_005',
                  label: 'office_building',
                  bboxes: [[0, 0, 400, 300]],
                  attributes: { style: 'modern', material: 'glass' },
                },
              ],
            },
          ],
        },
        {
          image_id: 'img_005',
          annotationGroups: [
            {
              label: 'scene',
              annotations: [
                {
                  id: 'ann_006',
                  label: 'outdoor_scene',
                  bboxes: [[0, 0, 400, 300]],
                  attributes: { time: 'daytime', weather: 'sunny' },
                },
              ],
            },
          ],
        },
      ],
    };

    // 将整个新数据结构作为一行数据
    const tableData: DatasetDetailItem[] = [
      {
        id: 1,
        createdAt: Date.now(),
        createdBy: 1,
        updatedAt: Date.now(),
        updatedBy: 1,
        fileUuid: 'new-structure-uuid-1',
        datasetDetail: {
          qa: JSON.stringify(newDataStructure.qa, null, 2), // qa数组直接转为字符串
          images: newDataStructure.images, // images保持原格式用于标注显示
          total_qa_pairs: newDataStructure.qa.length,
          total_images: newDataStructure.images.length,
          data_structure_type: 'NewDataStructure',
          // 保持原有的messages格式以兼容现有代码
          messages: [
            {
              role: 'system',
              content: 'New data structure with QA pairs and image annotations',
            },
          ],
        },
      },
    ];

    return tableData;
  };

  // 切换虚拟数据
  const toggleVirtualData = () => {
    setUseVirtualData(!useVirtualData);
    if (!useVirtualData) {
      // 切换到虚拟数据
      const virtualData = generateVirtualDataset();
      setDataList(virtualData);
      setPagination({
        current: 1,
        pageSize: 10,
        total: virtualData.length,
      });
      changTotalData(virtualData.length);
    } else {
      // 切换回真实数据
      fetchDetail();
    }
  };

  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({ datasetUuid, page, pageSize });
      if (res.code === 0) {
        const list = Array.isArray(res.data) ? res.data : res.data.list;
        setDataList(list || []); // 只保存列表数据，不再设置详情

        if (res.data.page) {
          setPagination({
            current: res.data.page,
            pageSize: res.data.pageSize || 10,
            total: res.data.total || 0,
          });
          changTotalData(res.data.total || 0);
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (record: DatasetDetailItem) => {
    setExpandedRowKeys((prev) =>
      prev.includes(String(record.id))
        ? prev.filter((key) => key !== String(record.id))
        : [...prev, String(record.id)],
    );
  };
  const columns: ColumnsType<DatasetDetailItem> = useMemo(() => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    // 定义友好的列标题映射
    const columnTitleMap: Record<string, string> = {
      qa: t('问答对数据'),
      images: t('图片标注数据'),
      total_qa_pairs: t('问答对总数'),
      total_images: t('图片总数'),
      data_structure_type: t('数据结构类型'),
      messages: t('对话消息'),
    };

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: columnTitleMap[key] || key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());

          // 特殊处理新数据结构的字段
          if (key === 'qa') {
            // qa字段直接显示为格式化的JSON字符串
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
                style={{
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap',
                }}>
                {value}
              </div>
            );
          }

          if (key === 'images') {
            if (!value || !Array.isArray(value)) return '-';
            return (
              <div>
                {value.map((image: ImageItem, index: number) => (
                  <Tag key={index} color='blue' style={{ marginBottom: 4 }}>
                    {image.image_id} ({image.annotationGroups.length} 组)
                  </Tag>
                ))}
              </div>
            );
          }

          if (key === 'total_qa_pairs' || key === 'total_images') {
            return <Tag color='green'>{value}</Tag>;
          }

          if (key === 'data_structure_type') {
            return <Tag color='purple'>{value}</Tag>;
          }

          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  }, [dataList, expandedRowKeys]);

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <>
      {/* 控制按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type={useVirtualData ? 'primary' : 'default'}
            onClick={toggleVirtualData}>
            {useVirtualData ? t('切换到真实数据') : t('切换到虚拟数据')}
          </Button>
          {useVirtualData && (
            <span style={{ color: '#1890ff', fontSize: '12px' }}>
              {t(
                '当前显示新格式虚拟数据：包含问答对(QA)和图片标注信息，点击图片可查看详细标注',
              )}
            </span>
          )}
        </Space>
      </div>

      <Table
        bordered
        rowKey='id'
        columns={columns}
        dataSource={dataList}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          style: { cursor: 'pointer' },
        })}
        scroll={{ x: 'max-content' }}
      />
    </>
  );
};

export default DataPart;
